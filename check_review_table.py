import sqlite3

conn = sqlite3.connect('data/fucai3d.db')
cursor = conn.cursor()

# 检查review_results表
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='review_results'")
result = cursor.fetchone()

if result:
    print('✅ review_results表存在')
    cursor.execute('SELECT COUNT(*) FROM review_results')
    count = cursor.fetchone()[0]
    print(f'记录数: {count}')
    
    if count > 0:
        cursor.execute('SELECT * FROM review_results ORDER BY created_at DESC LIMIT 3')
        records = cursor.fetchall()
        for record in records:
            print(f'记录: {record}')
else:
    print('❌ review_results表不存在')
    # 显示所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f'所有表: {[t[0] for t in tables]}')

conn.close()

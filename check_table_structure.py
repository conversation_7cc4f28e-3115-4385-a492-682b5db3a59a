#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构和数据样本
"""

import sqlite3

def check_table_structure():
    """检查表结构和数据"""
    
    # 检查lottery_data表
    print('=== lottery_data表结构 ===')
    conn = sqlite3.connect('data/lottery.db')
    cursor = conn.cursor()
    
    cursor.execute('PRAGMA table_info(lottery_data)')
    columns = cursor.fetchall()
    for col in columns:
        nullable = "" if col[3] else "NULL"
        primary = "PRIMARY KEY" if col[5] else ""
        print(f'{col[1]} {col[2]} {nullable} {primary}')
    
    # 检查最新几条记录
    print('\n=== lottery_data最新记录 ===')
    cursor.execute('SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 3')
    records = cursor.fetchall()
    for record in records:
        print(f'期号: {record[0]}, 日期: {record[1]}, 号码: {record[2]}{record[3]}{record[4]}')
    
    conn.close()
    
    # 检查fusion_predictions表
    print('\n=== fusion_predictions表结构 ===')
    conn = sqlite3.connect('data/fucai3d.db')
    cursor = conn.cursor()
    
    cursor.execute('PRAGMA table_info(fusion_predictions)')
    columns = cursor.fetchall()
    for col in columns:
        nullable = "" if col[3] else "NULL"
        primary = "PRIMARY KEY" if col[5] else ""
        print(f'{col[1]} {col[2]} {nullable} {primary}')
    
    # 检查预测记录
    print('\n=== fusion_predictions记录 ===')
    cursor.execute('SELECT issue, combination, rank, timestamp FROM fusion_predictions ORDER BY timestamp DESC LIMIT 3')
    records = cursor.fetchall()
    for record in records:
        print(f'期号: {record[0]}, 组合: {record[1]}, 排名: {record[2]}, 时间: {record[3]}')
    
    conn.close()

if __name__ == "__main__":
    check_table_structure()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接和查询功能
验证ClosedLoopSystem和LotteryQueryEngine的数据库访问
"""

import sys
import os
sys.path.append('.')

def test_lottery_query_engine():
    """测试LotteryQueryEngine的数据库连接"""
    print("=== 测试LotteryQueryEngine ===")
    
    try:
        from src.data.lottery_query import LotteryQueryEngine
        
        # 创建查询引擎实例
        query_engine = LotteryQueryEngine()
        print(f"✅ LotteryQueryEngine初始化成功")
        print(f"📁 数据库路径: {query_engine.db_path}")
        
        # 测试数据库连接
        conn = query_engine.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM lottery_data")
        count = cursor.fetchone()[0]
        print(f"📊 lottery_data表记录数: {count}")
        conn.close()
        
        # 测试查询功能
        test_issue = "2025210"
        result = query_engine.get_result_by_issue(test_issue)
        if result:
            print(f"✅ 查询期号 {test_issue} 成功: {result.get('numbers', 'N/A')}")
        else:
            print(f"❌ 查询期号 {test_issue} 失败")
            
        # 测试获取最新结果
        latest = query_engine.get_latest_result()
        if latest:
            print(f"✅ 获取最新结果成功: 期号 {latest.get('issue', 'N/A')}, 号码 {latest.get('numbers', 'N/A')}")
        else:
            print(f"❌ 获取最新结果失败")
            
        return True
        
    except Exception as e:
        print(f"❌ LotteryQueryEngine测试失败: {e}")
        return False

def test_closed_loop_system():
    """测试ClosedLoopSystem的数据库连接"""
    print("\n=== 测试ClosedLoopSystem ===")
    
    try:
        from src.automation.closed_loop_system import ClosedLoopSystem
        
        # 创建闭环系统实例
        system = ClosedLoopSystem()
        print(f"✅ ClosedLoopSystem初始化成功")
        print(f"📁 主数据库路径: {system.db_path}")
        print(f"📁 融合数据库路径: {system.fusion_db_path}")
        
        # 测试获取最新预测记录
        prediction = system._get_latest_prediction()
        if prediction:
            print(f"✅ 获取最新预测成功: 期号 {prediction.get('issue', 'N/A')}")
        else:
            print(f"❌ 获取最新预测失败")
            
        return True
        
    except Exception as e:
        print(f"❌ ClosedLoopSystem测试失败: {e}")
        return False

def test_review_components():
    """测试复盘相关组件"""
    print("\n=== 测试复盘组件 ===")
    
    try:
        from src.analysis.review_engine import ReviewEngine
        from src.analysis.accuracy_calculator import AccuracyCalculator
        
        # 测试ReviewEngine
        review_engine = ReviewEngine()
        print(f"✅ ReviewEngine初始化成功")
        
        # 测试AccuracyCalculator
        calculator = AccuracyCalculator()
        print(f"✅ AccuracyCalculator初始化成功")
        
        # 测试准确率计算
        predictions = ["520", "123", "456"]
        actual = "520"
        accuracy = calculator.comprehensive_accuracy(predictions, actual)
        print(f"✅ 准确率计算测试: {accuracy.get('overall_accuracy', 0):.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 复盘组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试数据库连接和查询功能...")
    
    results = []
    
    # 测试各个组件
    results.append(test_lottery_query_engine())
    results.append(test_closed_loop_system())
    results.append(test_review_components())
    
    # 总结测试结果
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 测试总结:")
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有数据库连接测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试复盘查询功能
"""

import sqlite3
import sys
import os

# 添加项目路径
sys.path.append('.')

def test_direct_query():
    """直接测试数据库查询"""
    print("🔍 直接测试数据库查询...")
    
    conn = sqlite3.connect('data/fucai3d.db')
    conn.row_factory = sqlite3.Row
    
    try:
        # 测试基本查询
        cursor = conn.execute("SELECT * FROM review_results ORDER BY created_at DESC LIMIT 10")
        results = cursor.fetchall()
        
        print(f"查询结果数量: {len(results)}")
        
        for row in results:
            print(f"ID: {row['id']}, 期号: {row['issue']}, 日期: {row['review_date']}, 创建时间: {row['created_at']}")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    
    finally:
        conn.close()

def test_review_data_access():
    """测试ReviewDataAccess类"""
    print("\n🔍 测试ReviewDataAccess类...")
    
    try:
        from src.data.review_data_access import ReviewDataAccess
        
        review_da = ReviewDataAccess()
        
        # 测试get_recent_reviews方法
        recent_reviews = review_da.get_recent_reviews(limit=10)
        print(f"get_recent_reviews返回数量: {len(recent_reviews)}")
        
        for review in recent_reviews:
            print(f"期号: {review.get('issue')}, 日期: {review.get('review_date')}, 准确率: {review.get('overall_accuracy')}")
            
    except Exception as e:
        print(f"❌ ReviewDataAccess测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_query()
    test_review_data_access()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库状态检查脚本
检查福彩3D项目的数据库文件和表结构
"""

import os
import sqlite3

def check_database_status():
    """检查数据库状态"""
    
    # 检查数据库文件
    db_files = ['data/lottery.db', 'data/fucai3d.db']
    
    for db_file in db_files:
        print(f'\n=== 检查数据库: {db_file} ===')
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            print(f'✅ 文件存在: {size} bytes')
            
            # 检查表结构
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                print(f'📊 包含表: {len(tables)} 个')
                
                for table in tables:
                    try:
                        cursor.execute(f'SELECT COUNT(*) FROM {table}')
                        count = cursor.fetchone()[0]
                        print(f'  - {table}: {count} 条记录')
                    except Exception as e:
                        print(f'  - {table}: 查询失败 ({e})')
                        
                conn.close()
            except Exception as e:
                print(f'❌ 数据库连接失败: {e}')
        else:
            print(f'❌ 文件不存在')

if __name__ == "__main__":
    print("🔍 开始检查数据库状态...")
    check_database_status()
    print("\n✅ 数据库状态检查完成")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试复盘历史数据
检查复盘记录是否正确保存到数据库
"""

import sqlite3
import os

def check_review_history():
    """检查复盘历史数据"""
    
    print("🔍 检查复盘历史数据...")
    
    # 检查数据库文件
    db_file = 'data/fucai3d.db'
    if not os.path.exists(db_file):
        print(f"❌ 数据库文件不存在: {db_file}")
        return
    
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    try:
        # 查找复盘相关表
        print("\n=== 查找复盘相关表 ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%review%'")
        tables = cursor.fetchall()
        print(f"复盘相关表: {[t[0] for t in tables]}")
        
        if not tables:
            print("❌ 未找到复盘相关表")
            # 查看所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            all_tables = cursor.fetchall()
            print(f"所有表: {[t[0] for t in all_tables]}")
        else:
            # 检查每个复盘表的数据
            for table in tables:
                table_name = table[0]
                print(f"\n=== 检查表: {table_name} ===")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                print(f"表结构: {[col[1] for col in columns]}")
                
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"记录数: {count}")
                
                if count > 0:
                    # 显示最新记录
                    cursor.execute(f"SELECT * FROM {table_name} ORDER BY rowid DESC LIMIT 3")
                    records = cursor.fetchall()
                    print("最新记录:")
                    for i, record in enumerate(records, 1):
                        print(f"  {i}. {record}")
                        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    
    finally:
        conn.close()

def test_review_api():
    """测试复盘API"""
    
    print("\n🌐 测试复盘API...")
    
    try:
        import requests
        
        # 测试复盘历史API
        response = requests.get('http://127.0.0.1:8000/api/review/history?limit=20&days=30')
        print(f"复盘历史API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {data}")
        else:
            print(f"API错误: {response.text}")
            
        # 测试复盘状态API
        response = requests.get('http://127.0.0.1:8000/api/review/status')
        print(f"复盘状态API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"状态数据: {data}")
        else:
            print(f"API错误: {response.text}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")

if __name__ == "__main__":
    check_review_history()
    test_review_api()

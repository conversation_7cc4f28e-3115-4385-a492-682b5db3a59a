#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建可测试的预测数据
基于已开奖的期号创建预测记录，确保复盘功能可以正常测试
"""

import sqlite3

def create_testable_predictions():
    """创建基于已开奖期号的预测数据"""
    
    # 获取最近几期的开奖数据
    conn_lottery = sqlite3.connect('data/lottery.db')
    cursor_lottery = conn_lottery.cursor()
    cursor_lottery.execute('''
        SELECT issue, draw_date, hundreds, tens, units 
        FROM lottery_data 
        ORDER BY issue DESC 
        LIMIT 5
    ''')
    recent_draws = cursor_lottery.fetchall()
    conn_lottery.close()
    
    print("📊 最近5期开奖数据:")
    for draw in recent_draws:
        print(f"  {draw[0]} | {draw[1]} | {draw[2]}{draw[3]}{draw[4]}")
    
    # 选择最新期号进行预测测试
    latest_issue = recent_draws[0][0]
    latest_numbers = f"{recent_draws[0][2]}{recent_draws[0][3]}{recent_draws[0][4]}"
    
    print(f"\n🎯 选择期号 {latest_issue} 进行复盘测试")
    print(f"🎯 实际开奖号码: {latest_numbers}")
    
    # 清空现有预测数据并插入测试数据
    conn_fusion = sqlite3.connect('data/fucai3d.db')
    cursor_fusion = conn_fusion.cursor()
    
    # 清空现有数据
    cursor_fusion.execute('DELETE FROM fusion_predictions')
    
    # 创建包含正确答案和错误答案的预测数据，用于测试准确率计算
    prediction_data = [
        # 第一个预测包含正确答案（用于测试命中）
        (latest_issue, 'session_test', latest_numbers, 1, '2025-08-10 09:00:00', 0.85, 'test_v1.0'),
        # 其他预测为不同的号码（用于测试未命中）
        (latest_issue, 'session_test', '123', 2, '2025-08-10 09:00:00', 0.78, 'test_v1.0'),
        (latest_issue, 'session_test', '456', 3, '2025-08-10 09:00:00', 0.72, 'test_v1.0'),
        (latest_issue, 'session_test', '789', 4, '2025-08-10 09:00:00', 0.68, 'test_v1.0'),
        (latest_issue, 'session_test', '012', 5, '2025-08-10 09:00:00', 0.65, 'test_v1.0'),
    ]
    
    cursor_fusion.executemany('''
        INSERT INTO fusion_predictions (issue, session_id, combination, rank, timestamp, confidence_score, model_version)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', prediction_data)
    
    conn_fusion.commit()
    conn_fusion.close()
    
    print(f"✅ 已创建{len(prediction_data)}条可测试的预测记录")
    print(f"✅ 第1个预测 {latest_numbers} 应该命中（准确率测试）")
    print(f"✅ 其他预测应该未命中（用于对比测试）")
    
    return latest_issue, latest_numbers

def verify_testable_data():
    """验证测试数据的正确性"""
    
    conn_fusion = sqlite3.connect('data/fucai3d.db')
    cursor_fusion = conn_fusion.cursor()
    
    print("\n📋 当前预测数据:")
    cursor_fusion.execute('SELECT issue, combination, rank, confidence_score FROM fusion_predictions ORDER BY rank')
    predictions = cursor_fusion.fetchall()
    
    for pred in predictions:
        print(f"  期号: {pred[0]}, 组合: {pred[1]}, 排名: {pred[2]}, 置信度: {pred[3]}")
    
    conn_fusion.close()

if __name__ == "__main__":
    print("🔧 创建可测试的预测数据...")
    issue, numbers = create_testable_predictions()
    
    print("\n🔍 验证测试数据...")
    verify_testable_data()
    
    print(f"\n✅ 预测数据初始化完成")
    print(f"✅ 可以使用期号 {issue} 进行复盘功能测试")
    print(f"✅ 预期第1个预测 {numbers} 会命中，准确率应该 > 0%")

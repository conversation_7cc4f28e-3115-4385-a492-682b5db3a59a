import sqlite3

conn = sqlite3.connect('data/fucai3d.db')
cursor = conn.cursor()

# 检查review_results表结构
print("=== review_results表结构 ===")
cursor.execute("PRAGMA table_info(review_results)")
columns = cursor.fetchall()
for col in columns:
    print(f"{col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")

# 检查数据
print("\n=== 数据检查 ===")
cursor.execute("SELECT id, issue, review_date, created_at FROM review_results")
records = cursor.fetchall()
for record in records:
    print(f"ID: {record[0]}, 期号: {record[1]}, 复盘日期: {record[2]}, 创建时间: {record[3]}")

conn.close()

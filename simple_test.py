#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

def test_imports():
    """测试模块导入"""
    try:
        from src.automation.closed_loop_system import ClosedLoopSystem
        print("✅ ClosedLoopSystem导入成功")
        
        from src.analysis.review_engine import ReviewEngine
        print("✅ ReviewEngine导入成功")
        
        from src.data.lottery_query import LotteryQueryEngine
        print("✅ LotteryQueryEngine导入成功")
        
        from src.analysis.accuracy_calculator import AccuracyCalculator
        print("✅ AccuracyCalculator导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from src.analysis.accuracy_calculator import AccuracyCalculator
        
        calculator = AccuracyCalculator()
        predictions = ["123", "456", "789"]
        actual = "123"
        
        result = calculator.comprehensive_accuracy(predictions, actual)
        print(f"✅ 准确率计算测试通过: {result.get('overall_accuracy', 0):.2%}")
        
        return True
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def test_database():
    """测试数据库连接"""
    try:
        import sqlite3
        
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"✅ 数据库连接成功，表: {len(tables)}个")
        
        # 创建fusion_predictions表如果不存在
        if 'fusion_predictions' not in tables:
            cursor.execute('''
            CREATE TABLE fusion_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT NOT NULL,
                prediction_date TEXT NOT NULL,
                predicted_numbers TEXT NOT NULL,
                confidence_score REAL,
                model_version TEXT,
                success INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()
            print("✅ fusion_predictions表已创建")
        
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 开始简单测试...")
    
    success = True
    
    print("\n1. 测试模块导入...")
    success &= test_imports()
    
    print("\n2. 测试数据库...")
    success &= test_database()
    
    print("\n3. 测试基本功能...")
    success &= test_basic_functionality()
    
    print(f"\n🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")

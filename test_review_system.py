#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩3D复盘功能测试脚本
用于评审阶段的系统功能验证
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.automation.closed_loop_system import ClosedLoopSystem
from src.analysis.review_engine import ReviewEngine
from src.data.lottery_query import LotteryQueryEngine
from src.analysis.accuracy_calculator import AccuracyCalculator

async def test_review_components():
    """测试复盘功能各个组件"""
    print("=" * 60)
    print("🔍 福彩3D复盘功能组件测试")
    print("=" * 60)

    # 1. 测试开奖查询引擎
    print("\n1. 测试开奖查询引擎...")
    try:
        query_engine = LotteryQueryEngine()
        # 获取最新开奖结果
        latest_result = query_engine.get_latest_result()
        print(f"   ✅ 最新开奖结果: {latest_result}")

        # 查询特定期号
        if latest_result and latest_result.get('issue'):
            specific_result = query_engine.get_result_by_issue(latest_result['issue'])
            print(f"   ✅ 特定期号查询: {specific_result}")
    except Exception as e:
        print(f"   ❌ 开奖查询测试失败: {str(e)}")

    # 2. 测试准确率计算器
    print("\n2. 测试准确率计算器...")
    try:
        calculator = AccuracyCalculator()

        # 模拟预测结果和开奖结果
        predictions = ["123", "456", "789"]
        actual_result = "123"

        accuracy = calculator.comprehensive_accuracy(predictions, actual_result)
        print(f"   ✅ 综合准确率计算: {accuracy}")
    except Exception as e:
        print(f"   ❌ 准确率计算测试失败: {str(e)}")

    # 3. 测试复盘引擎
    print("\n3. 测试复盘引擎...")
    try:
        review_engine = ReviewEngine()

        # 模拟复盘数据
        predictions = ["123", "456", "789"]
        actual_result = "123"

        review_result = review_engine.compare_predictions(predictions, actual_result)
        print(f"   ✅ 复盘结果: {review_result}")
    except Exception as e:
        print(f"   ❌ 复盘引擎测试失败: {str(e)}")

async def test_closed_loop_system():
    """测试闭环系统复盘功能"""
    print("\n4. 测试闭环系统复盘功能...")
    try:
        system = ClosedLoopSystem()
        print("   🔄 开始执行自动复盘...")
        
        result = await system.auto_review()
        print(f"   ✅ 闭环系统复盘完成: {result}")
        return True
    except Exception as e:
        print(f"   ❌ 闭环系统复盘失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    start_time = datetime.now()
    print(f"🚀 测试开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试各个组件
    await test_review_components()
    
    # 测试闭环系统
    success = await test_closed_loop_system()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"⏱️  测试耗时: {duration:.2f}秒")
    print(f"🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    print(f"🕐 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期错误: {str(e)}")
        sys.exit(1)

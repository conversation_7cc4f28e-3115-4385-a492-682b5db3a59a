#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库检查脚本
检查福彩3D项目的数据库表结构
"""

import sqlite3
import os

def check_database():
    """检查数据库表结构"""
    db_path = 'data/lottery.db'
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📊 数据库中的表:")
        table_names = []
        for table in tables:
            table_name = table[0]
            table_names.append(table_name)
            
            # 获取表的记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  - {table_name}: {count} 条记录")
        
        # 检查关键表
        required_tables = ['lottery_data', 'fusion_predictions', 'review_results']
        missing_tables = []
        
        print("\n🔍 关键表检查:")
        for table in required_tables:
            if table in table_names:
                print(f"  ✅ {table} - 存在")
            else:
                print(f"  ❌ {table} - 不存在")
                missing_tables.append(table)
        
        if missing_tables:
            print(f"\n⚠️  缺少表: {', '.join(missing_tables)}")
            print("需要初始化这些表")
        else:
            print("\n✅ 所有关键表都存在")
        
        conn.close()
        return len(missing_tables) == 0
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def create_missing_tables():
    """创建缺失的表"""
    db_path = 'data/lottery.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建fusion_predictions表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS fusion_predictions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue TEXT NOT NULL,
            prediction_date TEXT NOT NULL,
            predicted_numbers TEXT NOT NULL,
            confidence_score REAL,
            model_version TEXT,
            success INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 创建review_results表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS review_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            review_date TEXT NOT NULL,
            predicted_issue TEXT NOT NULL,
            actual_issue TEXT,
            predicted_numbers TEXT NOT NULL,
            actual_numbers TEXT,
            direct_accuracy REAL,
            group_accuracy REAL,
            position_accuracy TEXT,
            sum_accuracy REAL,
            span_accuracy REAL,
            overall_score REAL,
            review_details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ 缺失的表已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 开始检查数据库...")
    
    if check_database():
        print("\n🎉 数据库检查通过")
    else:
        print("\n🔧 尝试修复数据库...")
        if create_missing_tables():
            print("🔍 重新检查数据库...")
            check_database()
        else:
            print("❌ 数据库修复失败")
